{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7202da2d._.js", "server/edge/chunks/[root-of-the-server]__46a35d2f._.js", "server/edge/chunks/edge-wrapper_19de4db6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Zc3QBC/Ib0w9zwch8edztGN8MKpLEsKhu/ingB88CrA=", "__NEXT_PREVIEW_MODE_ID": "6d5e473598459cb513a7a6f0b3fdd830", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "913fc0fe4a661f17fb59bcd75b3522397f7fe8dfc073a0434b1ad25817b19279", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5b264853a6eace13822a07e85775190a6b8cc09a4fb9ae0c0a00be1a48b0c271"}}}, "sortedMiddleware": ["/"], "functions": {}}