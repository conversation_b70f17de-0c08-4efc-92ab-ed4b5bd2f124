{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/external_services/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,6PAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/external_services/src/lib/auth.ts"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function requireAuth() {\n  const supabase = await createClient()\n  \n  const { data, error } = await supabase.auth.getUser()\n  if (error || !data?.user) {\n    redirect('/auth/login')\n  }\n  \n  return data.user\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACnD,IAAI,SAAS,CAAC,MAAM,MAAM;QACxB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,OAAO,KAAK,IAAI;AAClB", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/external_services/src/components/onboarding-flow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OnboardingFlow = registerClientReference(\n    function() { throw new Error(\"Attempted to call OnboardingFlow() from the server but OnboardingFlow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/onboarding-flow.tsx <module evaluation>\",\n    \"OnboardingFlow\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/external_services/src/components/onboarding-flow.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OnboardingFlow = registerClientReference(\n    function() { throw new Error(\"Attempted to call OnboardingFlow() from the server but OnboardingFlow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/onboarding-flow.tsx\",\n    \"OnboardingFlow\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/external_services/src/app/onboarding/page.tsx"], "sourcesContent": ["import { requireAuth } from \"@/lib/auth\";\nimport { OnboardingFlow } from \"@/components/onboarding-flow\";\n\nexport default async function OnboardingPage() {\n  const user = await requireAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-background to-accent/5\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold text-foreground mb-2\">\n              Welcome to Transcription!\n            </h1>\n            <p className=\"text-muted-foreground\">\n              Let's get you set up with your organization and team\n            </p>\n          </div>\n          \n          <OnboardingFlow user={user} />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAE7B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC,wIAAA,CAAA,iBAAc;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}]}