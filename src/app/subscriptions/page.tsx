import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Navigation } from "@/components/navigation";
import { Progress } from "@/components/ui/progress";
import { requireAuth } from "@/lib/auth";
import {
  CreditCard,
  Calendar,
  AlertTriangle,
  TrendingUp,
  DollarSign
} from "lucide-react";

export default async function Subscriptions() {
  const user = await requireAuth();
  const subscriptions = [
    {
      id: 1,
      service: "Slack",
      plan: "Pro",
      price: 8,
      billing: "monthly",
      renewalDate: "2024-06-15",
      status: "Active",
      daysUntilRenewal: 18,
      paymentMethod: "Visa ****4242"
    },
    {
      id: 2,
      service: "Notion",
      plan: "Team",
      price: 10,
      billing: "monthly",
      renewalDate: "2024-06-20",
      status: "Active",
      daysUntilRenewal: 23,
      paymentMethod: "MasterCard ****8888"
    },
    {
      id: 3,
      service: "Figma",
      plan: "Professional",
      price: 15,
      billing: "monthly",
      renewalDate: "2024-06-02",
      status: "Expiring",
      daysUntilRenewal: 5,
      paymentMethod: "Visa ****4242"
    },
    {
      id: 4,
      service: "Vercel",
      plan: "Pro",
      price: 20,
      billing: "monthly",
      renewalDate: "2024-07-01",
      status: "Active",
      daysUntilRenewal: 34,
      paymentMethod: "Visa ****4242"
    }
  ];

  const totalMonthly = subscriptions.reduce((sum, sub) => sum + sub.price, 0);
  const totalAnnual = totalMonthly * 12;

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Subscriptions</h1>
          <p className="text-muted-foreground">
            Track and manage your subscription costs and renewals
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Total</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalMonthly}</div>
              <p className="text-xs text-muted-foreground">
                +$8 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Annual Projection</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${totalAnnual}</div>
              <p className="text-xs text-muted-foreground">
                Based on current subscriptions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">1</div>
              <p className="text-xs text-muted-foreground">
                Next 7 days
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Subscriptions List */}
        <Card>
          <CardHeader>
            <CardTitle>Active Subscriptions</CardTitle>
            <CardDescription>
              Manage your subscription plans and billing information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {subscriptions.map((subscription) => (
                <div key={subscription.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{subscription.service}</h3>
                      <p className="text-sm text-muted-foreground">
                        {subscription.plan} Plan • {subscription.paymentMethod}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    <div className="text-right">
                      <p className="font-semibold">${subscription.price}/{subscription.billing}</p>
                      <p className="text-sm text-muted-foreground">
                        Renews {subscription.renewalDate}
                      </p>
                    </div>

                    <div className="text-right min-w-[100px]">
                      <Badge
                        variant={subscription.status === "Active" ? "secondary" : "destructive"}
                        className="mb-2"
                      >
                        {subscription.status}
                      </Badge>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span>Renewal</span>
                          <span>{subscription.daysUntilRenewal}d</span>
                        </div>
                        <Progress
                          value={Math.max(0, 100 - (subscription.daysUntilRenewal / 30) * 100)}
                          className="h-1"
                        />
                      </div>
                    </div>

                    <Button variant="outline" size="sm">
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
