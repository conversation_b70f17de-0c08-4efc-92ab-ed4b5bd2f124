import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Navigation } from "@/components/navigation";
import { requireAuth } from "@/lib/auth";
import {
  Globe,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default async function Portals() {
  const user = await requireAuth();
  const portals = [
    {
      id: 1,
      name: "Slack",
      url: "slack.com",
      category: "Communication",
      status: "Active",
      subscription: "$8/month",
      lastAccessed: "2 hours ago",
      color: "bg-blue-500"
    },
    {
      id: 2,
      name: "Notion",
      url: "notion.so",
      category: "Productivity",
      status: "Active",
      subscription: "$10/month",
      lastAccessed: "1 day ago",
      color: "bg-purple-500"
    },
    {
      id: 3,
      name: "Figma",
      url: "figma.com",
      category: "Design",
      status: "Expiring",
      subscription: "$15/month",
      lastAccessed: "3 days ago",
      color: "bg-green-500"
    },
    {
      id: 4,
      name: "GitHub",
      url: "github.com",
      category: "Development",
      status: "Active",
      subscription: "Free",
      lastAccessed: "5 hours ago",
      color: "bg-gray-800"
    },
    {
      id: 5,
      name: "Vercel",
      url: "vercel.com",
      category: "Development",
      status: "Active",
      subscription: "$20/month",
      lastAccessed: "1 hour ago",
      color: "bg-black"
    },
    {
      id: 6,
      name: "Linear",
      url: "linear.app",
      category: "Project Management",
      status: "Inactive",
      subscription: "$8/month",
      lastAccessed: "2 weeks ago",
      color: "bg-indigo-500"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">External Portals</h1>
          <p className="text-muted-foreground">
            Manage your external portal access and credentials
          </p>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search portals..."
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>

        {/* Portals Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {portals.map((portal) => (
            <Card key={portal.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 ${portal.color} rounded-lg flex items-center justify-center`}>
                      <span className="text-white font-semibold">
                        {portal.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{portal.name}</CardTitle>
                      <CardDescription>{portal.url}</CardDescription>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Category</span>
                    <Badge variant="outline">{portal.category}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Status</span>
                    <Badge
                      variant={
                        portal.status === "Active" ? "secondary" :
                        portal.status === "Expiring" ? "destructive" : "outline"
                      }
                    >
                      {portal.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Subscription</span>
                    <span className="text-sm font-medium">{portal.subscription}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Last Accessed</span>
                    <span className="text-sm">{portal.lastAccessed}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
