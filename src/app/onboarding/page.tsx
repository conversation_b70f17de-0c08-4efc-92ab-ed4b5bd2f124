import { requireAuth } from "@/lib/auth";
import { OnboardingFlow } from "@/components/onboarding-flow";

export default async function OnboardingPage() {
  const user = await requireAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Welcome to Transcription!
            </h1>
            <p className="text-muted-foreground">
              Let's get you set up with your organization and team
            </p>
          </div>
          
          <OnboardingFlow user={user} />
        </div>
      </div>
    </div>
  );
}
