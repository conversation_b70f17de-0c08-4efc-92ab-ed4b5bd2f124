@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Supabase Brand Colors */
  --background: oklch(0.99 0.005 106);
  --foreground: oklch(0.15 0.02 106);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 106);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 106);
  --primary: oklch(0.49 0.18 162); /* Supabase Green */
  --primary-foreground: oklch(0.99 0.005 106);
  --secondary: oklch(0.96 0.01 106);
  --secondary-foreground: oklch(0.15 0.02 106);
  --muted: oklch(0.96 0.01 106);
  --muted-foreground: oklch(0.45 0.02 106);
  --accent: oklch(0.94 0.02 162);
  --accent-foreground: oklch(0.15 0.02 106);
  --destructive: oklch(0.65 0.2 25);
  --border: oklch(0.9 0.01 106);
  --input: oklch(0.9 0.01 106);
  --ring: oklch(0.49 0.18 162);
  --chart-1: oklch(0.49 0.18 162); /* Supabase Green */
  --chart-2: oklch(0.55 0.15 200); /* Supabase Blue */
  --chart-3: oklch(0.6 0.12 280); /* Purple */
  --chart-4: oklch(0.65 0.15 45); /* Orange */
  --chart-5: oklch(0.7 0.1 320); /* Pink */
  --sidebar: oklch(0.98 0.005 106);
  --sidebar-foreground: oklch(0.15 0.02 106);
  --sidebar-primary: oklch(0.49 0.18 162);
  --sidebar-primary-foreground: oklch(0.99 0.005 106);
  --sidebar-accent: oklch(0.94 0.02 162);
  --sidebar-accent-foreground: oklch(0.15 0.02 106);
  --sidebar-border: oklch(0.9 0.01 106);
  --sidebar-ring: oklch(0.49 0.18 162);
}

.dark {
  /* Supabase Dark Theme */
  --background: oklch(0.08 0.01 106);
  --foreground: oklch(0.95 0.005 106);
  --card: oklch(0.12 0.01 106);
  --card-foreground: oklch(0.95 0.005 106);
  --popover: oklch(0.12 0.01 106);
  --popover-foreground: oklch(0.95 0.005 106);
  --primary: oklch(0.55 0.18 162); /* Brighter Supabase Green for dark mode */
  --primary-foreground: oklch(0.08 0.01 106);
  --secondary: oklch(0.18 0.01 106);
  --secondary-foreground: oklch(0.95 0.005 106);
  --muted: oklch(0.18 0.01 106);
  --muted-foreground: oklch(0.65 0.01 106);
  --accent: oklch(0.22 0.02 162);
  --accent-foreground: oklch(0.95 0.005 106);
  --destructive: oklch(0.7 0.2 25);
  --border: oklch(0.25 0.01 106);
  --input: oklch(0.25 0.01 106);
  --ring: oklch(0.55 0.18 162);
  --chart-1: oklch(0.55 0.18 162); /* Supabase Green */
  --chart-2: oklch(0.6 0.15 200); /* Supabase Blue */
  --chart-3: oklch(0.65 0.12 280); /* Purple */
  --chart-4: oklch(0.7 0.15 45); /* Orange */
  --chart-5: oklch(0.75 0.1 320); /* Pink */
  --sidebar: oklch(0.1 0.01 106);
  --sidebar-foreground: oklch(0.95 0.005 106);
  --sidebar-primary: oklch(0.55 0.18 162);
  --sidebar-primary-foreground: oklch(0.08 0.01 106);
  --sidebar-accent: oklch(0.22 0.02 162);
  --sidebar-accent-foreground: oklch(0.95 0.005 106);
  --sidebar-border: oklch(0.25 0.01 106);
  --sidebar-ring: oklch(0.55 0.18 162);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
